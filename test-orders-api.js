// Simple test script to test the orders API endpoint
const fetch = require('node-fetch');

async function testOrdersAPI() {
  try {
    console.log('Testing orders API endpoint...');
    
    const response = await fetch('http://localhost:3002/api/orders', {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        // Add any authentication headers if needed
      }
    });
    
    console.log('Response status:', response.status);
    console.log('Response headers:', Object.fromEntries(response.headers.entries()));
    
    const data = await response.text();
    console.log('Response body:', data);
    
    if (response.ok) {
      try {
        const jsonData = JSON.parse(data);
        console.log('Parsed JSON:', JSON.stringify(jsonData, null, 2));
      } catch (e) {
        console.log('Response is not valid JSON');
      }
    }
    
  } catch (error) {
    console.error('Error testing API:', error);
  }
}

testOrdersAPI();
