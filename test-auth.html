<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Authentication Test</title>
    <script src="https://cdn.jsdelivr.net/npm/@supabase/supabase-js@2"></script>
    <style>
        body { font-family: Arial, sans-serif; max-width: 800px; margin: 0 auto; padding: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .success { background-color: #d4edda; border-color: #c3e6cb; }
        .error { background-color: #f8d7da; border-color: #f5c6cb; }
        .info { background-color: #d1ecf1; border-color: #bee5eb; }
        button { padding: 10px 15px; margin: 5px; cursor: pointer; }
        input { padding: 8px; margin: 5px; width: 200px; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 3px; overflow-x: auto; }
    </style>
</head>
<body>
    <h1>Bentakon Store Authentication Test</h1>
    
    <div class="test-section">
        <h2>Test Registration</h2>
        <input type="email" id="regEmail" placeholder="Email" value="<EMAIL>">
        <input type="password" id="regPassword" placeholder="Password" value="password123">
        <input type="text" id="regName" placeholder="Name" value="Test User">
        <button onclick="testRegistration()">Test Registration</button>
        <div id="regResult"></div>
    </div>

    <div class="test-section">
        <h2>Test Login</h2>
        <input type="email" id="loginEmail" placeholder="Email" value="<EMAIL>">
        <input type="password" id="loginPassword" placeholder="Password" value="password123">
        <button onclick="testLogin()">Test Login</button>
        <div id="loginResult"></div>
    </div>

    <div class="test-section">
        <h2>Current User Status</h2>
        <button onclick="checkCurrentUser()">Check Current User</button>
        <button onclick="testLogout()">Logout</button>
        <div id="userStatus"></div>
    </div>

    <div class="test-section">
        <h2>Test Profile Access</h2>
        <button onclick="testProfileAccess()">Test Profile Access</button>
        <div id="profileResult"></div>
    </div>

    <script>
        // Initialize Supabase client
        const supabaseUrl = 'https://yobmtulnduwycpstkffo.supabase.co'
        const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InlvYm10dWxuZHV3eWNwc3RrZmZvIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTI4MDE1MTEsImV4cCI6MjA2ODM3NzUxMX0.geUMyayuq-V7-PKLm3bK0YC179dr_XbaMUcmbEHPeGA'
        const supabase = window.supabase.createClient(supabaseUrl, supabaseKey)

        function showResult(elementId, message, type = 'info') {
            const element = document.getElementById(elementId)
            element.innerHTML = `<div class="${type}"><pre>${message}</pre></div>`
        }

        async function testRegistration() {
            const email = document.getElementById('regEmail').value
            const password = document.getElementById('regPassword').value
            const name = document.getElementById('regName').value

            try {
                showResult('regResult', 'Testing registration...', 'info')
                
                const { data, error } = await supabase.auth.signUp({
                    email: email,
                    password: password,
                    options: {
                        data: {
                            name: name
                        }
                    }
                })

                if (error) {
                    showResult('regResult', `Registration failed: ${error.message}`, 'error')
                } else {
                    showResult('regResult', `Registration successful!\nUser ID: ${data.user?.id}\nEmail: ${data.user?.email}`, 'success')
                    
                    // Wait a moment then check if profile was created
                    setTimeout(async () => {
                        await testProfileAccess()
                    }, 2000)
                }
            } catch (err) {
                showResult('regResult', `Registration error: ${err.message}`, 'error')
            }
        }

        async function testLogin() {
            const email = document.getElementById('loginEmail').value
            const password = document.getElementById('loginPassword').value

            try {
                showResult('loginResult', 'Testing login...', 'info')
                
                const { data, error } = await supabase.auth.signInWithPassword({
                    email: email,
                    password: password
                })

                if (error) {
                    showResult('loginResult', `Login failed: ${error.message}`, 'error')
                } else {
                    showResult('loginResult', `Login successful!\nUser ID: ${data.user?.id}\nEmail: ${data.user?.email}`, 'success')
                    await checkCurrentUser()
                }
            } catch (err) {
                showResult('loginResult', `Login error: ${err.message}`, 'error')
            }
        }

        async function checkCurrentUser() {
            try {
                const { data: { user } } = await supabase.auth.getUser()
                
                if (user) {
                    showResult('userStatus', `Current user:\nID: ${user.id}\nEmail: ${user.email}\nCreated: ${user.created_at}`, 'success')
                } else {
                    showResult('userStatus', 'No user logged in', 'info')
                }
            } catch (err) {
                showResult('userStatus', `Error checking user: ${err.message}`, 'error')
            }
        }

        async function testProfileAccess() {
            try {
                showResult('profileResult', 'Testing profile access...', 'info')
                
                const { data: { user } } = await supabase.auth.getUser()
                
                if (!user) {
                    showResult('profileResult', 'No user logged in - cannot test profile access', 'error')
                    return
                }

                const { data, error } = await supabase
                    .from('user_profiles')
                    .select('*')
                    .eq('id', user.id)
                    .single()

                if (error) {
                    showResult('profileResult', `Profile access failed: ${error.message}\nCode: ${error.code}\nDetails: ${error.details}`, 'error')
                } else {
                    showResult('profileResult', `Profile access successful!\n${JSON.stringify(data, null, 2)}`, 'success')
                }
            } catch (err) {
                showResult('profileResult', `Profile access error: ${err.message}`, 'error')
            }
        }

        async function testLogout() {
            try {
                const { error } = await supabase.auth.signOut()
                
                if (error) {
                    showResult('userStatus', `Logout failed: ${error.message}`, 'error')
                } else {
                    showResult('userStatus', 'Logged out successfully', 'success')
                }
            } catch (err) {
                showResult('userStatus', `Logout error: ${err.message}`, 'error')
            }
        }

        // Check initial user status
        window.onload = function() {
            checkCurrentUser()
        }
    </script>
</body>
</html>
