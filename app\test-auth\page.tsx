"use client"

import { useState, useEffect } from "react"
import { useAuth } from "../contexts/AuthContext"
import { toast } from "sonner"

export default function TestAuthPage() {
  const { authState } = useAuth()
  const { user, isLoading, isAuthenticated } = authState
  
  const [apiResponse, setApiResponse] = useState<any>(null)
  const [isApiLoading, setIsApiLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const testOrdersApi = async () => {
    try {
      setIsApiLoading(true)
      setError(null)
      
      console.log('Testing orders API...')
      const response = await fetch('/api/orders')
      
      const data = await response.json()
      console.log('API response:', data)
      setApiResponse(data)
      
      if (!response.ok) {
        throw new Error(`API error: ${response.status} ${response.statusText}`)
      }
      
      toast.success('API call successful')
    } catch (error) {
      console.error('Error testing API:', error)
      setError(error instanceof Error ? error.message : 'Unknown error')
      toast.error('API call failed')
    } finally {
      setIsApiLoading(false)
    }
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <h1 className="text-3xl font-bold mb-6">Authentication Test Page</h1>
      
      <div className="bg-gray-800 p-6 rounded-lg mb-6">
        <h2 className="text-xl font-semibold mb-4">Auth State</h2>
        <div className="space-y-2">
          <p>Loading: {isLoading ? 'Yes' : 'No'}</p>
          <p>Authenticated: {isAuthenticated ? 'Yes' : 'No'}</p>
          <p>User ID: {user?.id || 'Not logged in'}</p>
          <p>User Email: {user?.email || 'Not logged in'}</p>
        </div>
      </div>
      
      <div className="bg-gray-800 p-6 rounded-lg mb-6">
        <h2 className="text-xl font-semibold mb-4">Test API</h2>
        <button 
          onClick={testOrdersApi}
          disabled={isApiLoading || !isAuthenticated}
          className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg disabled:opacity-50"
        >
          {isApiLoading ? 'Loading...' : 'Test Orders API'}
        </button>
        
        {error && (
          <div className="mt-4 p-4 bg-red-900/50 border border-red-700 rounded-lg">
            <p className="text-red-400">{error}</p>
          </div>
        )}
        
        {apiResponse && (
          <div className="mt-4">
            <h3 className="font-semibold mb-2">API Response:</h3>
            <pre className="bg-gray-900 p-4 rounded-lg overflow-auto max-h-96 text-sm">
              {JSON.stringify(apiResponse, null, 2)}
            </pre>
          </div>
        )}
      </div>
    </div>
  )
}
