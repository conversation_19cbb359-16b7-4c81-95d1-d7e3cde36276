import { z } from "zod"

// User validation schemas
export const userSchema = z.object({
  id: z.string().min(1, "معرف المستخدم مطلوب"),
  email: z.string().email("البريد الإلكتروني غير صالح"),
  name: z.string().min(2, "الاسم يجب أن يكون على الأقل حرفين").max(50, "الاسم لا يمكن أن يزيد عن 50 حرف"),
  role: z.enum(["admin", "distributor", "user"], {
    errorMap: () => ({ message: "نوع المستخدم غير صالح" })
  }),
  walletBalance: z.number().min(0, "رصيد المحفظة لا يمكن أن يكون سالب"),
  avatar: z.string().url("رابط الصورة غير صالح").optional(),
})

export const userUpdateSchema = userSchema.partial().omit({ id: true })

export const userRegistrationSchema = z.object({
  email: z.string().email("البريد الإلكتروني غير صالح"),
  name: z.string().min(2, "الاسم يجب أن يكون على الأقل حرفين").max(50, "الاسم لا يمكن أن يزيد عن 50 حرف"),
  password: z.string().min(8, "كلمة المرور يجب أن تكون على الأقل 8 أحرف")
    .regex(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/, "كلمة المرور يجب أن تحتوي على حرف كبير وصغير ورقم"),
  confirmPassword: z.string(),
}).refine((data) => data.password === data.confirmPassword, {
  message: "كلمات المرور غير متطابقة",
  path: ["confirmPassword"],
})

// Product validation schemas
export const packageSchema = z.object({
  id: z.string().min(1, "معرف الحزمة مطلوب"),
  name: z.string().min(1, "اسم الحزمة مطلوب").max(100, "اسم الحزمة لا يمكن أن يزيد عن 100 حرف"),
  price: z.number().min(0, "السعر لا يمكن أن يكون سالب"),
  originalPrice: z.number().min(0, "السعر الأصلي لا يمكن أن يكون سالب").optional(),
  discount: z.number().min(0).max(100, "الخصم يجب أن يكون بين 0 و 100").optional(),
  image: z.string().url("رابط الصورة غير صالح"),
  description: z.string().max(500, "الوصف لا يمكن أن يزيد عن 500 حرف").optional(),
  hasDigitalCodes: z.boolean().default(false),
  availableCodesCount: z.number().min(0, "عدد الأكواد المتاحة لا يمكن أن يكون سالب").optional(),
})

export const customFieldSchema = z.object({
  id: z.string().min(1, "معرف الحقل مطلوب"),
  label: z.string().min(1, "تسمية الحقل مطلوبة").max(100, "تسمية الحقل لا يمكن أن تزيد عن 100 حرف"),
  type: z.enum(["text", "email", "number"], {
    errorMap: () => ({ message: "نوع الحقل غير صالح" })
  }),
  required: z.boolean().default(false),
  placeholder: z.string().max(200, "النص التوضيحي لا يمكن أن يزيد عن 200 حرف"),
})

export const dropdownOptionSchema = z.object({
  value: z.string().min(1, "قيمة الخيار مطلوبة"),
  label: z.string().min(1, "تسمية الخيار مطلوبة"),
})

export const dropdownSchema = z.object({
  id: z.string().min(1, "معرف القائمة مطلوب"),
  label: z.string().min(1, "تسمية القائمة مطلوبة").max(100, "تسمية القائمة لا يمكن أن تزيد عن 100 حرف"),
  options: z.array(dropdownOptionSchema).min(1, "يجب أن تحتوي القائمة على خيار واحد على الأقل"),
  required: z.boolean().default(false),
})

export const productSchema = z.object({
  id: z.string().min(1, "معرف المنتج مطلوب"),
  slug: z.string().min(1, "الرابط المختصر مطلوب").regex(/^[a-z0-9-]+$/, "الرابط المختصر يجب أن يحتوي على أحرف صغيرة وأرقام وشرطات فقط"),
  title: z.string().min(1, "عنوان المنتج مطلوب").max(200, "عنوان المنتج لا يمكن أن يزيد عن 200 حرف"),
  description: z.string().min(10, "الوصف يجب أن يكون على الأقل 10 أحرف").max(2000, "الوصف لا يمكن أن يزيد عن 2000 حرف"),
  coverImage: z.string().url("رابط صورة الغلاف غير صالح"),
  category: z.string().min(1, "فئة المنتج مطلوبة"),
  tags: z.array(z.string()).default([]),
  rating: z.number().min(0).max(5, "التقييم يجب أن يكون بين 0 و 5").default(0),
  commentCount: z.number().min(0, "عدد التعليقات لا يمكن أن يكون سالب").default(0),
  packages: z.array(packageSchema).min(1, "المنتج يجب أن يحتوي على حزمة واحدة على الأقل"),
  customFields: z.array(customFieldSchema).optional(),
  dropdowns: z.array(dropdownSchema).optional(),
  featured: z.boolean().default(false),
  popular: z.boolean().default(false),
})

export const productCreateSchema = productSchema.omit({ id: true, rating: true, commentCount: true })
export const productUpdateSchema = productSchema.partial().omit({ id: true })

// Order validation schemas
export const orderSchema = z.object({
  id: z.string().min(1, "معرف الطلب مطلوب"),
  userId: z.string().min(1, "معرف المستخدم مطلوب"),
  productId: z.string().min(1, "معرف المنتج مطلوب"),
  packageId: z.string().min(1, "معرف الحزمة مطلوب"),
  amount: z.number().min(0, "مبلغ الطلب لا يمكن أن يكون سالب"),
  status: z.enum(["pending", "completed", "failed"], {
    errorMap: () => ({ message: "حالة الطلب غير صالحة" })
  }),
  createdAt: z.string().datetime("تاريخ الإنشاء غير صالح"),
  customData: z.record(z.any()).optional(),
})

export const orderCreateSchema = orderSchema.omit({ id: true, createdAt: true })
export const orderUpdateSchema = orderSchema.partial().omit({ id: true, createdAt: true })

// Homepage configuration schemas
export const bannerSlideSchema = z.object({
  id: z.string().min(1, "معرف البانر مطلوب"),
  title: z.string().min(1, "عنوان البانر مطلوب").max(100, "عنوان البانر لا يمكن أن يزيد عن 100 حرف").transform(sanitizeString),
  subtitle: z.string().max(200, "العنوان الفرعي لا يمكن أن يزيد عن 200 حرف").optional().transform(val => val ? sanitizeString(val) : val),
  image: z.string().url("رابط صورة البانر غير صالح").refine(url => {
    const sanitized = sanitizeUrl(url)
    return sanitized === url && (url.startsWith('https://') || url.startsWith('http://'))
  }, "رابط الصورة غير آمن"),
  linkType: z.enum(["product", "collection", "custom", "none"], {
    errorMap: () => ({ message: "نوع الرابط غير صالح" })
  }),
  linkValue: z.string().optional().transform(val => val ? sanitizeUrl(val) : val),
  active: z.boolean().default(true),
  order: z.number().min(0, "ترتيب البانر لا يمكن أن يكون سالب"),
})

export const homepageSectionSchema = z.object({
  id: z.string().min(1, "معرف القسم مطلوب"),
  title: z.string().min(1, "عنوان القسم مطلوب").max(100, "عنوان القسم لا يمكن أن يزيد عن 100 حرف").transform(sanitizeString),
  productIds: z.array(z.string().min(1, "معرف المنتج مطلوب")),
  order: z.number().min(0, "ترتيب القسم لا يمكن أن يكون سالب"),
  active: z.boolean().default(true),
})

// Search and filter schemas
export const searchSchema = z.object({
  query: z.string().max(100, "استعلام البحث لا يمكن أن يزيد عن 100 حرف").optional(),
  category: z.string().optional(),
  minPrice: z.number().min(0, "الحد الأدنى للسعر لا يمكن أن يكون سالب").optional(),
  maxPrice: z.number().min(0, "الحد الأقصى للسعر لا يمكن أن يكون سالب").optional(),
  featured: z.boolean().optional(),
  popular: z.boolean().optional(),
  tags: z.array(z.string()).optional(),
}).refine((data) => {
  if (data.minPrice && data.maxPrice) {
    return data.minPrice <= data.maxPrice
  }
  return true
}, {
  message: "الحد الأدنى للسعر يجب أن يكون أقل من أو يساوي الحد الأقصى",
  path: ["maxPrice"],
})

// Form validation schemas
export const contactFormSchema = z.object({
  name: z.string().min(2, "الاسم يجب أن يكون على الأقل حرفين").max(50, "الاسم لا يمكن أن يزيد عن 50 حرف"),
  email: z.string().email("البريد الإلكتروني غير صالح"),
  subject: z.string().min(5, "الموضوع يجب أن يكون على الأقل 5 أحرف").max(100, "الموضوع لا يمكن أن يزيد عن 100 حرف"),
  message: z.string().min(10, "الرسالة يجب أن تكون على الأقل 10 أحرف").max(1000, "الرسالة لا يمكن أن تزيد عن 1000 حرف"),
})

// API response schemas
export const apiResponseSchema = z.object({
  success: z.boolean(),
  data: z.any().optional(),
  error: z.string().optional(),
  message: z.string().optional(),
})

// Utility functions for validation
export function validateData<T>(schema: z.ZodSchema<T>, data: unknown): { success: true; data: T } | { success: false; errors: string[] } {
  try {
    const validatedData = schema.parse(data)
    return { success: true, data: validatedData }
  } catch (error) {
    if (error instanceof z.ZodError) {
      const errors = error.errors.map(err => err.message)
      return { success: false, errors }
    }
    return { success: false, errors: ["خطأ في التحقق من صحة البيانات"] }
  }
}

// Import DOMPurify for client-side sanitization
let DOMPurify: any = null
if (typeof window !== 'undefined') {
  // Only import on client side
  import('dompurify').then(module => {
    DOMPurify = module.default
  })
}

export function sanitizeString(input: string): string {
  if (!input) return ''

  // Server-side sanitization (basic)
  if (typeof window === 'undefined') {
    return input
      .replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '')
      .replace(/<[^>]*>/g, '')
      .replace(/javascript:/gi, '')
      .replace(/on\w+\s*=/gi, '')
      .replace(/data:/gi, '')
      .trim()
  }

  // Client-side sanitization (comprehensive)
  if (DOMPurify) {
    return DOMPurify.sanitize(input, {
      ALLOWED_TAGS: [], // No HTML tags allowed
      ALLOWED_ATTR: [],
      KEEP_CONTENT: true
    })
  }

  // Fallback sanitization
  return input
    .replace(/<[^>]*>/g, '')
    .replace(/javascript:/gi, '')
    .replace(/on\w+\s*=/gi, '')
    .replace(/data:/gi, '')
    .trim()
}

export function sanitizeUrl(url: string): string {
  if (!url) return ''

  // Allow only safe protocols
  const allowedProtocols = ['http:', 'https:', 'mailto:', 'tel:']

  try {
    const urlObj = new URL(url)
    if (!allowedProtocols.includes(urlObj.protocol)) {
      return ''
    }
    return url
  } catch {
    // Invalid URL
    return ''
  }
}

export function sanitizeObject<T extends Record<string, any>>(obj: T): T {
  const sanitized = {} as T
  for (const [key, value] of Object.entries(obj)) {
    if (typeof value === 'string') {
      sanitized[key as keyof T] = sanitizeString(value) as T[keyof T]
    } else if (typeof value === 'object' && value !== null && !Array.isArray(value)) {
      sanitized[key as keyof T] = sanitizeObject(value) as T[keyof T]
    } else {
      sanitized[key as keyof T] = value
    }
  }
  return sanitized
}
