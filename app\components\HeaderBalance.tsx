"use client"

import { useState, useEffect, useRef } from 'react'
import { ChevronDown, Wallet, RefreshCw } from 'lucide-react'
import { useData } from '../contexts/DataContext'
import { useAuth } from '../contexts/AuthContext'
import { getCurrencySymbol } from '../utils/currency'

export default function HeaderBalance() {
  const { currentUser } = useAuth()
  const { 
    currencies, 
    selectedCurrency, 
    setSelectedCurrency, 
    userBalances, 
    refreshUserBalances,
    formatPrice 
  } = useData()
  
  const [isDropdownOpen, setIsDropdownOpen] = useState(false)
  const [isRefreshing, setIsRefreshing] = useState(false)
  const dropdownRef = useRef<HTMLDivElement>(null)

  // Close dropdown when clicking outside
  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsDropdownOpen(false)
      }
    }

    document.addEventListener('mousedown', handleClickOutside)
    return () => document.removeEventListener('mousedown', handleClickOutside)
  }, [])

  // Don't show if user is not logged in
  if (!currentUser) {
    return null
  }

  const selectedBalance = userBalances[selectedCurrency] || 0
  const selectedCurrencyData = currencies.find(c => c.code === selectedCurrency)

  const handleCurrencyChange = (currencyCode: string) => {
    setSelectedCurrency(currencyCode)
    setIsDropdownOpen(false)
  }

  const handleRefreshBalance = async () => {
    setIsRefreshing(true)
    await refreshUserBalances()
    setTimeout(() => setIsRefreshing(false), 1000) // Show spinner for at least 1 second
  }

  return (
    <div className="relative" ref={dropdownRef}>
      {/* Balance Display */}
      <button
        onClick={() => setIsDropdownOpen(!isDropdownOpen)}
        className="flex items-center space-x-2 space-x-reverse bg-gradient-to-r from-purple-600/20 to-blue-600/20 backdrop-blur-sm border border-purple-500/30 rounded-xl px-4 py-2 text-white hover:from-purple-600/30 hover:to-blue-600/30 transition-all duration-300"
      >
        <Wallet className="w-5 h-5 text-purple-400" />
        <div className="text-right">
          <div className="text-sm font-medium">
            {selectedCurrencyData ? (
              `${selectedBalance.toFixed(2)} ${getCurrencySymbol(selectedCurrencyData.code)}`
            ) : (
              `${selectedBalance.toFixed(2)} ${getCurrencySymbol(selectedCurrency)}`
            )}
          </div>
          <div className="text-xs text-gray-400">رصيد المحفظة</div>
        </div>
        <ChevronDown className={`w-4 h-4 text-gray-400 transition-transform duration-200 ${isDropdownOpen ? 'rotate-180' : ''}`} />
      </button>

      {/* Dropdown Menu */}
      {isDropdownOpen && (
        <div className="absolute left-0 mt-2 w-80 bg-gray-800/95 backdrop-blur-md border border-gray-700/50 rounded-xl shadow-2xl z-50">
          {/* Header */}
          <div className="p-4 border-b border-gray-700/50">
            <div className="flex items-center justify-between">
              <h3 className="text-lg font-semibold text-white">محفظة {currentUser.name}</h3>
              <button
                onClick={handleRefreshBalance}
                disabled={isRefreshing}
                className="p-2 rounded-lg hover:bg-gray-700/50 transition-colors disabled:opacity-50"
              >
                <RefreshCw className={`w-4 h-4 text-gray-400 ${isRefreshing ? 'animate-spin' : ''}`} />
              </button>
            </div>
          </div>

          {/* Current Balance */}
          <div className="p-4 border-b border-gray-700/50">
            <div className="text-center">
              <div className="text-2xl font-bold text-white mb-1">
                {selectedCurrencyData ? (
                  `${selectedBalance.toFixed(2)} ${getCurrencySymbol(selectedCurrencyData.code)}`
                ) : (
                  `${selectedBalance.toFixed(2)} ${getCurrencySymbol(selectedCurrency)}`
                )}
              </div>
              <div className="text-sm text-gray-400">
                {selectedCurrencyData?.name || selectedCurrency}
              </div>
            </div>
          </div>

          {/* Currency Selector */}
          <div className="p-4 border-b border-gray-700/50">
            <h4 className="text-sm font-medium text-gray-300 mb-3">اختر العملة</h4>
            <div className="space-y-2">
              {currencies.map((currency) => {
                const balance = userBalances[currency.code] || 0
                const isSelected = currency.code === selectedCurrency
                
                return (
                  <button
                    key={currency.code}
                    onClick={() => handleCurrencyChange(currency.code)}
                    className={`w-full flex items-center justify-between p-3 rounded-lg transition-colors ${
                      isSelected 
                        ? 'bg-purple-600/30 border border-purple-500/50' 
                        : 'hover:bg-gray-700/50'
                    }`}
                  >
                    <div className="flex items-center space-x-3 space-x-reverse">
                      <div className="text-2xl">
                        {getCurrencySymbol(currency.code as any)}
                      </div>
                      <div className="text-left">
                        <div className="text-sm font-medium text-white">
                          {currency.code}
                        </div>
                        <div className="text-xs text-gray-400">
                          {currency.name}
                        </div>
                      </div>
                    </div>
                    <div className="text-right">
                      <div className="text-sm font-bold text-white">
                        {balance.toFixed(2)}
                      </div>
                      {currency.exchange_rate !== 1 && (
                        <div className="text-xs text-gray-400">
                          1 USD = {currency.exchange_rate}
                        </div>
                      )}
                    </div>
                  </button>
                )
              })}
            </div>
          </div>

          {/* All Balances Summary */}
          {Object.keys(userBalances).length > 1 && (
            <div className="p-4">
              <h4 className="text-sm font-medium text-gray-300 mb-3">جميع الأرصدة</h4>
              <div className="space-y-2">
                {Object.entries(userBalances).map(([currencyCode, balance]) => {
                  const currency = currencies.find(c => c.code === currencyCode)
                  if (!currency) return null
                  
                  return (
                    <div key={currencyCode} className="flex justify-between items-center text-sm">
                      <span className="text-gray-400">{currency.name}</span>
                      <span className="text-white font-medium">
                        {balance.toFixed(2)} {getCurrencySymbol(currencyCode as any)}
                      </span>
                    </div>
                  )
                })}
              </div>
            </div>
          )}

          {/* Quick Actions */}
          <div className="p-4 border-t border-gray-700/50">
            <div className="grid grid-cols-2 gap-2">
              <button
                onClick={() => {
                  setIsDropdownOpen(false)
                  // Navigate to wallet page
                  window.location.href = '/wallet'
                }}
                className="px-3 py-2 bg-blue-600 hover:bg-blue-700 text-white text-sm font-medium rounded-lg transition-colors"
              >
                إدارة المحفظة
              </button>
              <button
                onClick={() => {
                  setIsDropdownOpen(false)
                  // Navigate to wallet page to see orders
                  window.location.href = '/wallet'
                }}
                className="px-3 py-2 bg-gray-600 hover:bg-gray-700 text-white text-sm font-medium rounded-lg transition-colors"
              >
                تاريخ الطلبات
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
