# Multi-Tenant Setup Guide

## Overview

This guide walks you through setting up the Bentakon Store as a multi-tenant SaaS platform. Each tenant represents a separate gaming storefront with its own products, users, branding, and configuration.

## Architecture Overview

### Multi-Tenant Model
- **Shared Database**: All tenants share the same database with tenant_id isolation
- **Tenant Resolution**: Automatic tenant detection via domain/subdomain
- **Data Isolation**: Complete separation using Row Level Security (RLS)
- **Custom Branding**: Per-tenant themes, logos, and styling
- **Feature Toggles**: Tenant-specific feature configuration

### Tenant Resolution Methods
1. **Custom Domain**: `tenant1.com` → Tenant 1
2. **Subdomain**: `tenant1.bentakon.com` → Tenant 1  
3. **Development**: Environment variable fallback

## Prerequisites

- Supabase project with PostgreSQL database
- Next.js 14+ application
- Node.js 18+ and npm/yarn
- Domain management access (for custom domains)

## Step 1: Environment Configuration

Create or update your `.env.local` file:

```bash
# Multi-Tenant Configuration
NEXT_PUBLIC_MULTI_TENANT_MODE=true
NEXT_PUBLIC_DEFAULT_TENANT_SLUG=main
NEXT_PUBLIC_ENABLE_CUSTOM_DOMAINS=true
NEXT_PUBLIC_ENABLE_SUBDOMAINS=true

# Supabase Configuration
NEXT_PUBLIC_SUPABASE_URL=your_supabase_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_service_role_key

# Application Settings
NEXT_PUBLIC_APP_NAME=Bentakon Multi-Tenant
NEXT_PUBLIC_APP_URL=https://yourdomain.com

# Development Settings
NEXT_PUBLIC_MOCK_DATA=false
NEXT_PUBLIC_DEBUG_MODE=true
```

## Step 2: Database Setup

### 2.1 Run the Multi-Tenant Schema Migration

Execute the following SQL in your Supabase SQL Editor:

```sql
-- Enable required extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Create tenants table
CREATE TABLE tenants (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  name TEXT NOT NULL,
  slug TEXT UNIQUE NOT NULL,
  custom_domain TEXT UNIQUE,
  theme_config JSONB DEFAULT '{}',
  settings JSONB DEFAULT '{}',
  status TEXT NOT NULL DEFAULT 'active' CHECK (status IN ('active', 'inactive', 'suspended')),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes
CREATE UNIQUE INDEX idx_tenants_slug ON tenants(slug);
CREATE UNIQUE INDEX idx_tenants_custom_domain ON tenants(custom_domain) WHERE custom_domain IS NOT NULL;
CREATE INDEX idx_tenants_status ON tenants(status);

-- Enable RLS
ALTER TABLE tenants ENABLE ROW LEVEL SECURITY;

-- Create updated_at trigger function
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Apply trigger to tenants
CREATE TRIGGER update_tenants_updated_at BEFORE UPDATE ON tenants
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
```

### 2.2 Update Existing Tables

Add tenant_id to user_profiles and create other tables:

```sql
-- Add tenant_id to user_profiles
ALTER TABLE user_profiles 
ADD COLUMN tenant_id UUID REFERENCES tenants(id) ON DELETE CASCADE,
ADD COLUMN settings JSONB DEFAULT '{}';

-- Create index
CREATE INDEX idx_user_profiles_tenant_id ON user_profiles(tenant_id);
```

### 2.3 Create Default Tenant

```sql
-- Insert default 'main' tenant
INSERT INTO tenants (name, slug, theme_config, settings, status)
VALUES (
  'بنتاكون الرئيسي',
  'main',
  '{
    "primaryColor": "#3b82f6",
    "secondaryColor": "#1e40af",
    "accentColor": "#f59e0b",
    "backgroundColor": "#111827",
    "textColor": "#ffffff"
  }',
  '{
    "features": {
      "digitalCodes": true,
      "walletSystem": true,
      "customFields": true,
      "analytics": true
    },
    "limits": {
      "maxProducts": 1000,
      "maxUsers": 10000,
      "maxOrders": 100000
    }
  }',
  'active'
);

-- Update existing user_profiles to use main tenant
UPDATE user_profiles 
SET tenant_id = (SELECT id FROM tenants WHERE slug = 'main')
WHERE tenant_id IS NULL;

-- Make tenant_id required
ALTER TABLE user_profiles ALTER COLUMN tenant_id SET NOT NULL;
```

## Step 3: Application Deployment

### 3.1 Install Dependencies

```bash
npm install
# or
yarn install
```

### 3.2 Build and Deploy

```bash
npm run build
npm start
# or
yarn build
yarn start
```

### 3.3 Verify Installation

1. Visit your main domain
2. Check that the default tenant loads correctly
3. Verify tenant resolution in browser dev tools
4. Test admin functionality

## Step 4: Creating New Tenants

### 4.1 Via Database (Temporary)

```sql
INSERT INTO tenants (name, slug, custom_domain, theme_config, settings, status)
VALUES (
  'Gaming Store Pro',
  'gamingpro',
  'gamingpro.com',
  '{
    "primaryColor": "#ef4444",
    "secondaryColor": "#dc2626",
    "accentColor": "#f59e0b",
    "backgroundColor": "#111827",
    "textColor": "#ffffff",
    "logo": "https://example.com/logo.png"
  }',
  '{
    "features": {
      "digitalCodes": true,
      "walletSystem": true,
      "customFields": false,
      "analytics": true
    },
    "limits": {
      "maxProducts": 500,
      "maxUsers": 5000,
      "maxOrders": 50000
    }
  }',
  'active'
);
```

### 4.2 Via Admin Interface (Future)

A tenant management interface will be available for super admins to:
- Create new tenants
- Configure themes and branding
- Set feature toggles and limits
- Manage tenant status

## Step 5: Domain Configuration

### 5.1 Subdomain Setup

Configure DNS for wildcard subdomains:
```
*.yourdomain.com CNAME yourdomain.com
```

### 5.2 Custom Domain Setup

For each tenant with a custom domain:
1. Add DNS record: `CNAME tenant-domain.com yourdomain.com`
2. Configure SSL certificate
3. Update tenant record with custom_domain

## Step 6: Testing Multi-Tenant Setup

### 6.1 Test Tenant Isolation

1. Create test data for different tenants
2. Verify data separation between tenants
3. Test RLS policies are working
4. Confirm no cross-tenant data leakage

### 6.2 Test Tenant Resolution

1. Access via subdomain: `tenant1.yourdomain.com`
2. Access via custom domain: `tenant1.com`
3. Verify correct tenant data loads
4. Check theme application

### 6.3 Test Admin Functions

1. Login as tenant admin
2. Verify tenant-scoped admin access
3. Test product/order management
4. Confirm proper permissions

## Troubleshooting

### Common Issues

1. **Tenant Not Found**: Check DNS configuration and tenant slug
2. **Data Not Loading**: Verify RLS policies and tenant_id relationships
3. **Theme Not Applied**: Check theme_config JSON structure
4. **Permission Denied**: Verify user role and tenant association

### Debug Mode

Enable debug mode for detailed logging:
```bash
NEXT_PUBLIC_DEBUG_MODE=true
```

## Security Considerations

1. **RLS Policies**: Ensure all tenant-scoped tables have proper RLS
2. **Input Validation**: Validate tenant_id in all operations
3. **Super Admin Access**: Limit super admin privileges
4. **Audit Logging**: Monitor cross-tenant access attempts

## Performance Optimization

1. **Database Indexing**: Ensure tenant_id indexes on all tables
2. **Caching**: Implement tenant-aware caching strategies
3. **Connection Pooling**: Configure for multi-tenant load
4. **Monitoring**: Set up per-tenant performance metrics

## Next Steps

1. Implement tenant management UI
2. Add tenant analytics and reporting
3. Set up automated tenant provisioning
4. Configure backup and disaster recovery
5. Implement tenant-specific feature flags
