"use client"

import { useState, useEffect } from "react"
import Link from "next/link"
import { Tag } from "lucide-react"
import CategorySection from "./components/CategorySection"
import PromoBanner from "./components/PromoBanner"
import PaginatedProductSection from "./components/PaginatedProductSection"
import CategoryCard from "./components/CategoryCard"
import AdminSetup from "./components/AdminSetup"
import TenantSwitcher from "./components/TenantSwitcher"

import { useData } from "./contexts/DataContext"
import { useTenant } from "./contexts/TenantContext"
import { getCategories } from "./lib/categories"
import type { Category } from "./types"

export default function HomePage() {
  // Use centralized data context
  const { products, homepageSections, currentUser } = useData()
  const { tenant } = useTenant()
  const [categories, setCategories] = useState<Category[]>([])
  const userRole = currentUser?.role || "user"

  // Load categories for homepage display
  useEffect(() => {
    if (tenant) {
      loadCategories()
    }
  }, [tenant])

  const loadCategories = async () => {
    if (!tenant) return

    try {
      const result = await getCategories(tenant.id)
      if (result.success && result.data) {
        // Show only first 6 categories on homepage
        setCategories(result.data.slice(0, 6))
      }
    } catch (error) {
      console.error('Error loading categories:', error)
    }
  }

  // Calculate product count for each category
  const categoriesWithProductCount = categories.map(category => {
    const productCount = products.filter(product =>
      product.category_id === category.id || product.category === category.name
    ).length
    return { ...category, product_count: productCount }
  })

  // Get active homepage sections sorted by order
  const activeSections = homepageSections.filter((section) => section.active).sort((a, b) => a.order - b.order)

  return (
    <div className="min-h-screen bg-gray-900">
      {/* Promotional Banner */}
      <section className="container mx-auto px-4 pt-4 pb-2">
        <PromoBanner />
      </section>

      {/* Categories Section */}
      {categoriesWithProductCount.length > 0 && (
        <section className="container mx-auto px-4 py-6">
          <div className="flex items-center justify-between mb-6">
            <div>
              <h2 className="text-xl md:text-2xl font-bold text-white mb-2">
                🏷️ تصفح حسب الفئة
              </h2>
              <p className="text-gray-400">
                اكتشف منتجاتنا المنظمة حسب الفئات
              </p>
            </div>
            <Link
              href="/categories"
              className="text-purple-400 hover:text-purple-300 transition-colors text-sm font-medium flex items-center space-x-1 space-x-reverse"
            >
              <span>عرض الكل</span>
              <Tag className="w-4 h-4" />
            </Link>
          </div>

          {/* Categories Grid */}
          <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 gap-3 md:gap-4">
            {categoriesWithProductCount.map((category) => (
              <CategoryCard
                key={category.id}
                category={category}
                productCount={category.product_count}
              />
            ))}
          </div>
        </section>
      )}

      {/* Main Product Catalog */}
      <div className="container mx-auto px-4 py-4">
        <PaginatedProductSection
          title="🎮 جميع المنتجات"
          products={products}
          userRole={userRole}
          productsPerPage={20}
        />
      </div>

      {/* Featured Sections (Optional - can be removed if not needed) */}
      <div className="container mx-auto px-4 pb-8">
        {activeSections.slice(0, 2).map((section) => {
          const sectionProducts = products.filter((product) => section.productIds.includes(product.id))

          if (sectionProducts.length === 0) return null

          return (
            <CategorySection
              key={section.id}
              title={section.title}
              products={sectionProducts.slice(0, 8)} // Limit to 8 products for featured sections
              userRole={userRole}
            />
          )
        })}
      </div>

      {/* Development Tools (Development Only) */}
      <AdminSetup />
      <TenantSwitcher />
    </div>
  )
}
