# Migration Guide: Single-Tenant to Multi-Tenant

## Overview

This guide helps you migrate an existing single-tenant Bentakon Store installation to the new multi-tenant architecture while preserving all existing data and functionality.

## Pre-Migration Checklist

- [ ] **Backup Database**: Create a full backup of your current database
- [ ] **Test Environment**: Set up a test environment for migration testing
- [ ] **Downtime Planning**: Plan for maintenance window if needed
- [ ] **User Communication**: Notify users of upcoming changes
- [ ] **Rollback Plan**: Prepare rollback procedures if needed

## Migration Steps

### Step 1: Database Backup

Create a complete backup of your current database:

```bash
# Using Supabase CLI
supabase db dump --file backup-pre-migration.sql

# Or using pg_dump directly
pg_dump -h your-db-host -U your-username -d your-database > backup-pre-migration.sql
```

### Step 2: Update Application Code

1. **Pull Latest Code**:
   ```bash
   git pull origin main
   npm install
   ```

2. **Update Environment Variables**:
   Add multi-tenant configuration to your `.env.local`:
   ```bash
   # Add these new variables
   NEXT_PUBLIC_MULTI_TENANT_MODE=true
   NEXT_PUBLIC_DEFAULT_TENANT_SLUG=main
   NEXT_PUBLIC_ENABLE_CUSTOM_DOMAINS=false
   NEXT_PUBLIC_ENABLE_SUBDOMAINS=false
   ```

### Step 3: Database Schema Migration

Execute the following SQL commands in your Supabase SQL Editor:

#### 3.1 Create Tenants Table

```sql
-- Create tenants table
CREATE TABLE tenants (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  name TEXT NOT NULL,
  slug TEXT UNIQUE NOT NULL,
  custom_domain TEXT UNIQUE,
  theme_config JSONB DEFAULT '{}',
  settings JSONB DEFAULT '{}',
  status TEXT NOT NULL DEFAULT 'active' CHECK (status IN ('active', 'inactive', 'suspended')),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes
CREATE UNIQUE INDEX idx_tenants_slug ON tenants(slug);
CREATE UNIQUE INDEX idx_tenants_custom_domain ON tenants(custom_domain) WHERE custom_domain IS NOT NULL;
CREATE INDEX idx_tenants_status ON tenants(status);

-- Enable RLS
ALTER TABLE tenants ENABLE ROW LEVEL SECURITY;

-- Create RLS policies
CREATE POLICY "Tenants are viewable by everyone" ON tenants FOR SELECT USING (true);
CREATE POLICY "Only super admins can modify tenants" ON tenants FOR ALL USING (
  EXISTS (
    SELECT 1 FROM user_profiles 
    WHERE id = auth.uid() 
    AND role = 'admin'
    AND (settings->>'is_super_admin')::boolean = true
  )
);

-- Create updated_at trigger function
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Apply trigger
CREATE TRIGGER update_tenants_updated_at BEFORE UPDATE ON tenants
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
```

#### 3.2 Create Default Tenant

```sql
-- Insert default 'main' tenant with your current branding
INSERT INTO tenants (name, slug, theme_config, settings, status)
VALUES (
  'بنتاكون الرئيسي', -- Your current store name
  'main',
  '{
    "primaryColor": "#3b82f6",
    "secondaryColor": "#1e40af",
    "accentColor": "#f59e0b",
    "backgroundColor": "#111827",
    "textColor": "#ffffff",
    "logo": null,
    "favicon": null,
    "customCSS": null
  }',
  '{
    "features": {
      "digitalCodes": true,
      "walletSystem": true,
      "customFields": true,
      "analytics": true
    },
    "limits": {
      "maxProducts": 10000,
      "maxUsers": 100000,
      "maxOrders": 1000000
    }
  }',
  'active'
);
```

#### 3.3 Update Existing Tables

```sql
-- Add tenant_id to user_profiles
ALTER TABLE user_profiles 
ADD COLUMN tenant_id UUID REFERENCES tenants(id) ON DELETE CASCADE,
ADD COLUMN settings JSONB DEFAULT '{}';

-- Create index
CREATE INDEX idx_user_profiles_tenant_id ON user_profiles(tenant_id);

-- Update existing user_profiles to use main tenant
UPDATE user_profiles 
SET tenant_id = (SELECT id FROM tenants WHERE slug = 'main')
WHERE tenant_id IS NULL;

-- Make tenant_id required
ALTER TABLE user_profiles ALTER COLUMN tenant_id SET NOT NULL;

-- Set default for new records
ALTER TABLE user_profiles 
ALTER COLUMN tenant_id SET DEFAULT (SELECT id FROM tenants WHERE slug = 'main');
```

#### 3.4 Create New Multi-Tenant Tables

If you have existing products, orders, or other data in separate tables, you'll need to create the new multi-tenant versions and migrate the data. See the complete schema in `DATABASE_SCHEMA.md`.

### Step 4: Data Migration

If you have existing data in custom tables, migrate it to the new multi-tenant structure:

```sql
-- Example: Migrate existing products (if you have them)
-- First create the new products table (see DATABASE_SCHEMA.md)
-- Then migrate data:

INSERT INTO products (
  tenant_id,
  slug,
  title,
  description,
  cover_image,
  category,
  tags,
  rating,
  comment_count,
  featured,
  popular,
  created_at,
  updated_at
)
SELECT 
  (SELECT id FROM tenants WHERE slug = 'main') as tenant_id,
  slug,
  title,
  description,
  cover_image,
  category,
  tags,
  rating,
  comment_count,
  featured,
  popular,
  created_at,
  updated_at
FROM old_products_table;

-- Repeat for other tables as needed
```

### Step 5: Update RLS Policies

Update existing RLS policies to be tenant-aware:

```sql
-- Example: Update user_profiles policies
DROP POLICY IF EXISTS "Users can view own profile" ON user_profiles;
DROP POLICY IF EXISTS "Users can update own profile" ON user_profiles;
DROP POLICY IF EXISTS "Admins can view all profiles" ON user_profiles;

-- Create new tenant-aware policies
CREATE POLICY "Users can view own profile" ON user_profiles 
FOR SELECT USING (auth.uid() = id);

CREATE POLICY "Users can update own profile" ON user_profiles 
FOR UPDATE USING (auth.uid() = id);

CREATE POLICY "Admins can view profiles in their tenant" ON user_profiles 
FOR SELECT USING (
  EXISTS (
    SELECT 1 FROM user_profiles up 
    WHERE up.id = auth.uid() 
    AND up.role = 'admin'
    AND (up.tenant_id = user_profiles.tenant_id OR (up.settings->>'is_super_admin')::boolean = true)
  )
);
```

### Step 6: Test Migration

1. **Verify Data Integrity**:
   ```sql
   -- Check all users have tenant_id
   SELECT COUNT(*) FROM user_profiles WHERE tenant_id IS NULL;
   
   -- Verify main tenant exists
   SELECT * FROM tenants WHERE slug = 'main';
   
   -- Check RLS policies are working
   SET ROLE authenticated;
   SELECT * FROM user_profiles; -- Should only show accessible records
   ```

2. **Test Application**:
   - Start the application in development mode
   - Verify existing functionality works
   - Test admin access
   - Check that data loads correctly

### Step 7: Deploy to Production

1. **Deploy Application**:
   ```bash
   npm run build
   # Deploy to your hosting platform
   ```

2. **Update Environment Variables**:
   - Update production environment variables
   - Enable multi-tenant mode
   - Set default tenant slug

3. **Monitor Application**:
   - Check application logs
   - Monitor database performance
   - Verify user access

## Post-Migration Tasks

### 1. Create Super Admin

Designate a super admin user who can manage all tenants:

```sql
UPDATE user_profiles 
SET settings = settings || '{"is_super_admin": true}'::jsonb 
WHERE id = 'your_admin_user_id' AND role = 'admin';
```

### 2. Update Documentation

- Update any internal documentation
- Inform users of new multi-tenant capabilities
- Document any changes in functionality

### 3. Plan Future Tenants

- Decide on tenant creation process
- Plan domain/subdomain strategy
- Set up monitoring for multi-tenant metrics

## Rollback Procedure

If you need to rollback the migration:

1. **Restore Database Backup**:
   ```bash
   # Restore from backup
   psql -h your-db-host -U your-username -d your-database < backup-pre-migration.sql
   ```

2. **Revert Code Changes**:
   ```bash
   git checkout previous-version-tag
   npm install
   ```

3. **Update Environment Variables**:
   - Remove multi-tenant configuration
   - Restore original settings

## Troubleshooting

### Common Issues

1. **RLS Policy Errors**:
   - Check policy syntax
   - Verify user roles and permissions
   - Test policies with different user types

2. **Data Access Issues**:
   - Verify tenant_id assignments
   - Check foreign key constraints
   - Ensure proper indexing

3. **Application Errors**:
   - Check environment variables
   - Verify API endpoints
   - Review application logs

### Getting Help

- Check the troubleshooting section in `MULTI_TENANT_SETUP_GUIDE.md`
- Review database logs for errors
- Test in development environment first

## Benefits After Migration

After successful migration, you'll have:

- **Scalability**: Ability to serve multiple tenants
- **Isolation**: Complete data separation between tenants
- **Customization**: Per-tenant branding and configuration
- **Growth**: Foundation for SaaS business model
- **Backward Compatibility**: Existing functionality preserved
