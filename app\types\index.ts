/**
 * Category Entity - Represents product categories with tenant isolation
 *
 * Database Table: categories
 * Primary Key: id
 * Indexes: tenant_id, slug, is_active, sort_order
 *
 * Relationships:
 * - Many-to-One with Tenant (tenant_id)
 * - One-to-Many with Product (category_id)
 */
export interface Category {
  id: string              // UUID - Primary key, auto-generated
  tenant_id: string       // UUID - Tenant identifier for multi-tenant support
  name?: string           // Optional display name of the category
  slug: string            // URL-friendly identifier, unique per tenant (required)
  description?: string    // Optional description for the category
  image: string           // Image URL for the category (required)
  created_at: string      // ISO timestamp of creation
  updated_at: string      // ISO timestamp of last update
  product_count?: number  // Virtual field - number of products in category
}

/**
 * Product Entity - Represents a game or digital service available for purchase
 *
 * Database Table: products
 * Primary Key: id
 * Indexes: slug (unique), category, category_id, featured, popular
 *
 * Relationships:
 * - Many-to-One with Category (category_id)
 * - One-to-Many with Package (packages)
 * - One-to-Many with CustomField (customFields)
 * - One-to-Many with Dropdown (dropdowns)
 * - Referenced by Order (productId)
 * - Referenced by HomepageSection (productIds array)
 */
export interface Product {
  id: string              // UUID - Primary key, auto-generated
  tenant_id?: string      // UUID - Tenant identifier for multi-tenant support
  slug: string            // URL-friendly identifier, unique per tenant
  title: string           // Display name of the product
  description: string     // Detailed description for product page
  coverImage: string      // URL to product cover image
  category: string        // Legacy: Product category string (for backward compatibility)
  category_id?: string    // UUID - Foreign key to categories table
  categoryData?: Category // Virtual field - populated category data
  tags: string[]          // Array of tags for filtering and search
  rating: number          // Average user rating (0-5)
  commentCount: number    // Number of user reviews/comments
  packages: Package[]     // Available purchase packages for this product
  customFields?: CustomField[]  // Custom input fields required for purchase
  dropdowns?: Dropdown[]  // Dropdown selection fields for purchase
  featured: boolean       // Whether product appears in featured sections
  popular: boolean        // Whether product appears in popular sections
}

/**
 * Package Entity - Represents different purchase options for a product
 *
 * Database Table: packages
 * Primary Key: id
 * Foreign Key: productId (references products.id)
 * Indexes: productId, hasDigitalCodes
 *
 * Relationships:
 * - Many-to-One with Product (productId)
 * - One-to-Many with DigitalCode (digitalCodes)
 * - Referenced by Order (packageId)
 */
export interface Package {
  id: string                    // UUID - Primary key
  name: string                  // Package name (e.g., "60 Diamonds")
  price: number                 // Current selling price in USD
  originalPrice?: number        // Original price before discount
  discount?: number             // Discount percentage (0-100)
  image: string                 // URL to package image or reference
  use_product_image?: boolean   // Whether to use parent product's image
  image_reference_type?: 'url' | 'product_image'  // Type of image reference
  description?: string          // Optional package description
  digitalCodes?: DigitalCode[]  // Associated digital codes for instant delivery
  hasDigitalCodes?: boolean     // Whether this package uses digital codes
  availableCodesCount?: number  // Number of unused codes available
}

/**
 * DigitalCode Entity - Represents redeemable codes for digital products
 *
 * Database Table: digital_codes
 * Primary Key: id
 * Foreign Key: packageId (references packages.id)
 * Indexes: packageId, used, assignedToOrderId
 *
 * Relationships:
 * - Many-to-One with Package (packageId)
 * - One-to-One with Order (assignedToOrderId)
 *
 * Security: Keys should be encrypted at rest
 */
export interface DigitalCode {
  id: string                      // UUID - Primary key
  key: string                     // The actual redeemable code (encrypted)
  used: boolean                   // Whether code has been assigned to an order
  assignedToOrderId: string | null // Order this code is assigned to
  assignedAt?: string             // Timestamp when code was assigned
  viewedCount?: number            // How many times customer viewed the code
  lastViewedAt?: string           // Last time customer viewed the code
}

/**
 * CustomField Entity - Defines custom input fields required for product purchase
 *
 * Database Table: custom_fields
 * Primary Key: id
 * Foreign Key: productId (references products.id)
 * Indexes: productId, required
 *
 * Used for collecting user-specific data like Player ID, Server ID, etc.
 */
export interface CustomField {
  id: string                              // UUID - Primary key
  label: string                           // Field label displayed to user
  type: "text" | "email" | "number"      // Input field type
  required: boolean                       // Whether field is mandatory
  placeholder: string                     // Placeholder text for input
}

/**
 * Dropdown Entity - Defines dropdown selection fields for product purchase
 *
 * Database Table: dropdowns
 * Primary Key: id
 * Foreign Key: productId (references products.id)
 * Indexes: productId, required
 */
export interface Dropdown {
  id: string                    // UUID - Primary key
  label: string                 // Dropdown label displayed to user
  options: DropdownOption[]     // Available options for selection
  required: boolean             // Whether selection is mandatory
}

/**
 * DropdownOption Entity - Individual options within a dropdown
 *
 * Database Table: dropdown_options
 * Primary Key: value (within dropdown context)
 * Foreign Key: dropdownId (references dropdowns.id)
 */
export interface DropdownOption {
  value: string    // Option value (used in forms)
  label: string    // Option label displayed to user
}

/**
 * User Entity - Represents system users with role-based access
 *
 * Database Table: users (managed by Supabase Auth)
 * Primary Key: id (UUID from Supabase Auth)
 * Indexes: email (unique), role
 *
 * Relationships:
 * - One-to-Many with Order (userId)
 *
 * Security: Integrated with Supabase Auth for authentication
 * Row Level Security (RLS) policies required for data protection
 */
export interface User {
  id: string                                    // UUID from Supabase Auth
  tenant_id?: string                            // UUID - Tenant identifier for multi-tenant support
  email: string                                 // User email (unique, from Auth)
  name: string                                  // Display name
  role: "admin" | "distributor" | "user"       // User role for access control
  walletBalance: number                         // User's wallet balance in USD
  avatar?: string                               // Optional profile picture URL
  phone?: string                                // Optional phone number
  createdAt?: string                            // Account creation date
}

/**
 * Order Entity - Represents customer purchase transactions
 *
 * Database Table: orders
 * Primary Key: id
 * Foreign Keys:
 *   - userId (references users.id)
 *   - productId (references products.id)
 *   - packageId (references packages.id)
 * Indexes: userId, productId, status, createdAt
 *
 * Relationships:
 * - Many-to-One with User (userId)
 * - Many-to-One with Product (productId)
 * - Many-to-One with Package (packageId)
 * - One-to-One with DigitalCode (via assignedToOrderId)
 *
 * Security: Users can only access their own orders (RLS policy)
 */
export interface Order {
  id: string                                    // UUID - Primary key
  tenant_id?: string                            // UUID - Tenant identifier for multi-tenant support
  userId: string                                // Customer who placed the order
  productId: string                             // Product being purchased
  packageId: string                             // Specific package/variant
  amount: number                                // Total amount paid in USD
  status: "pending" | "completed" | "failed"   // Order processing status
  createdAt: string                             // ISO timestamp of order creation
  customData?: Record<string, any>              // Custom field data (Player ID, etc.)
  digitalCode?: {                               // Associated digital code (if applicable)
    key: string                                 // The redeemable code
    revealed: boolean                           // Whether customer has viewed code
    revealedAt?: string                         // When code was first revealed
    viewCount: number                           // Number of times code was viewed
  }
}

/**
 * BannerSlide Entity - Represents promotional banners on homepage
 *
 * Database Table: banner_slides
 * Primary Key: id
 * Indexes: active, order
 *
 * Used for homepage carousel/banner management
 */
export interface BannerSlide {
  id: string                                          // UUID - Primary key
  tenant_id?: string                                  // UUID - Tenant identifier for multi-tenant support
  title: string                                       // Banner title text
  subtitle?: string                                   // Optional subtitle text
  image: string                                       // Banner image URL
  linkType: "product" | "collection" | "custom" | "none"  // Type of link action
  linkValue?: string                                  // Link target (product slug, URL, etc.)
  active: boolean                                     // Whether banner is currently shown
  order: number                                       // Display order (lower = first)
}

/**
 * HomepageSection Entity - Represents product sections on homepage
 *
 * Database Table: homepage_sections
 * Primary Key: id
 * Indexes: active, order
 *
 * Relationships:
 * - References Product (productIds array)
 *
 * Used for organizing products into themed sections
 */
export interface HomepageSection {
  id: string              // UUID - Primary key
  tenant_id?: string      // UUID - Tenant identifier for multi-tenant support
  title: string           // Section title (e.g., "🔥 Popular Games") - can include emojis directly
  productIds: string[]    // Array of product IDs to display
  order: number           // Display order on homepage
  active: boolean         // Whether section is currently shown
}

/**
 * HomepageConfig - Configuration object for homepage layout
 *
 * Not a database table - used for API responses and admin management
 */
export interface HomepageConfig {
  banners: BannerSlide[]      // Active banner slides
  sections: HomepageSection[] // Active homepage sections
}
