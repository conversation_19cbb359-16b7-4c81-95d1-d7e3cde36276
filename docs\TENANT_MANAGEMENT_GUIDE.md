# Tenant Management Guide

## Overview

This guide explains how to create, configure, and manage tenants in the Bentakon Store multi-tenant platform. Each tenant represents a separate gaming storefront with its own branding, products, users, and settings.

## Tenant Structure

A tenant consists of:

1. **Core Information**:
   - Unique identifier (UUID)
   - Name (display name)
   - Slug (URL-friendly identifier)
   - Custom domain (optional)
   - Status (active, inactive, suspended)

2. **Theme Configuration**:
   - Colors (primary, secondary, accent, background, text)
   - Logo and favicon
   - Custom CSS

3. **Feature Settings**:
   - Enabled/disabled features
   - Resource limits
   - Integration settings

## Creating a New Tenant

### Method 1: Direct Database Insertion

Until the admin interface is complete, tenants can be created via SQL:

```sql
INSERT INTO tenants (
  name, 
  slug, 
  custom_domain, 
  theme_config, 
  settings, 
  status
)
VALUES (
  'Gaming Store Pro',
  'gamingpro',
  'gamingpro.com',
  '{
    "primaryColor": "#ef4444",
    "secondaryColor": "#dc2626",
    "accentColor": "#f59e0b",
    "backgroundColor": "#111827",
    "textColor": "#ffffff",
    "logo": "https://example.com/logo.png",
    "favicon": "https://example.com/favicon.ico",
    "customCSS": null
  }',
  '{
    "features": {
      "digitalCodes": true,
      "walletSystem": true,
      "customFields": false,
      "analytics": true
    },
    "limits": {
      "maxProducts": 500,
      "maxUsers": 5000,
      "maxOrders": 50000
    }
  }',
  'active'
);
```

### Method 2: API Endpoint (Future)

A secure API endpoint will be available for tenant creation:

```javascript
// Example API call (future implementation)
const createTenant = async (tenantData) => {
  const response = await fetch('/api/admin/tenants', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${adminToken}`
    },
    body: JSON.stringify(tenantData)
  });
  return response.json();
};
```

### Method 3: Admin Interface (Future)

A visual interface for super admins will be available to create and manage tenants.

## Tenant Configuration

### Theme Customization

The `theme_config` JSON object supports the following properties:

| Property | Type | Description | Default |
|----------|------|-------------|---------|
| primaryColor | String | Main brand color | #3b82f6 |
| secondaryColor | String | Secondary brand color | #1e40af |
| accentColor | String | Highlight color | #f59e0b |
| backgroundColor | String | Page background | #111827 |
| textColor | String | Main text color | #ffffff |
| logo | String | URL to logo image | null |
| favicon | String | URL to favicon | null |
| customCSS | String | Custom CSS rules | null |

Example theme configuration:

```json
{
  "primaryColor": "#10b981",
  "secondaryColor": "#059669",
  "accentColor": "#f59e0b",
  "backgroundColor": "#0f172a",
  "textColor": "#f8fafc",
  "logo": "https://example.com/logo.png",
  "favicon": "https://example.com/favicon.ico",
  "customCSS": "body { font-family: 'Cairo', sans-serif; }"
}
```

### Feature Toggles

The `settings` JSON object supports the following structure:

```json
{
  "features": {
    "digitalCodes": true,
    "walletSystem": true,
    "customFields": true,
    "analytics": true
  },
  "limits": {
    "maxProducts": 1000,
    "maxUsers": 10000,
    "maxOrders": 100000
  }
}
```

Available feature toggles:

| Feature | Description |
|---------|-------------|
| digitalCodes | Enable digital code redemption |
| walletSystem | Enable user wallet functionality |
| customFields | Enable custom form fields for products |
| analytics | Enable tenant-specific analytics |

Resource limits:

| Limit | Description |
|-------|-------------|
| maxProducts | Maximum number of products |
| maxUsers | Maximum number of users |
| maxOrders | Maximum number of orders |

## Domain Configuration

### Custom Domain Setup

1. **DNS Configuration**:
   - Add a CNAME record pointing to your main domain
   - Example: `CNAME gamingpro.com yourdomain.com`

2. **SSL Certificate**:
   - Ensure SSL is configured for the custom domain
   - Use Let's Encrypt or similar service

3. **Update Tenant Record**:
   ```sql
   UPDATE tenants 
   SET custom_domain = 'gamingpro.com' 
   WHERE slug = 'gamingpro';
   ```

### Subdomain Setup

1. **DNS Configuration**:
   - Add a wildcard DNS record
   - Example: `*.yourdomain.com CNAME yourdomain.com`

2. **SSL Certificate**:
   - Ensure wildcard SSL certificate is configured
   - Example: `*.yourdomain.com`

3. **Tenant Slug**:
   - The tenant slug will be used as the subdomain
   - Example: `gamingpro.yourdomain.com` for slug `gamingpro`

## Managing Tenant Status

Tenants can have the following statuses:

| Status | Description |
|--------|-------------|
| active | Fully operational |
| inactive | Temporarily disabled |
| suspended | Permanently disabled |

To change a tenant's status:

```sql
UPDATE tenants 
SET status = 'inactive' 
WHERE slug = 'gamingpro';
```

## User Management

### Creating a Tenant Admin

1. Create a user in Supabase Auth
2. Create a user profile with admin role and tenant association:

```sql
INSERT INTO user_profiles (
  id, 
  tenant_id, 
  name, 
  role, 
  settings
)
VALUES (
  'auth_user_id_from_supabase',
  (SELECT id FROM tenants WHERE slug = 'gamingpro'),
  'Admin User',
  'admin',
  '{"is_tenant_admin": true}'
);
```

### Super Admin Access

Super admins can access all tenants:

```sql
UPDATE user_profiles 
SET settings = settings || '{"is_super_admin": true}'::jsonb 
WHERE id = 'auth_user_id_from_supabase';
```

## Monitoring and Analytics

### Tenant Usage Metrics

Query to get tenant usage statistics:

```sql
SELECT 
  t.name AS tenant_name,
  t.slug AS tenant_slug,
  COUNT(DISTINCT u.id) AS user_count,
  COUNT(DISTINCT p.id) AS product_count,
  COUNT(DISTINCT o.id) AS order_count
FROM 
  tenants t
LEFT JOIN 
  user_profiles u ON t.id = u.tenant_id
LEFT JOIN 
  products p ON t.id = p.tenant_id
LEFT JOIN 
  orders o ON t.id = o.tenant_id
GROUP BY 
  t.id, t.name, t.slug
ORDER BY 
  user_count DESC;
```

## Troubleshooting

### Common Issues

1. **Tenant Not Found**:
   - Verify tenant slug is correct
   - Check tenant status is 'active'
   - Ensure DNS configuration is correct

2. **Theme Not Applied**:
   - Verify theme_config JSON structure
   - Check for valid color values
   - Clear browser cache

3. **Feature Not Available**:
   - Check feature toggle in tenant settings
   - Verify user has appropriate permissions

## Best Practices

1. **Naming Conventions**:
   - Use descriptive tenant names
   - Keep slugs short and memorable
   - Avoid special characters in slugs

2. **Theme Consistency**:
   - Maintain color contrast for accessibility
   - Test themes on multiple devices
   - Keep custom CSS minimal

3. **Security**:
   - Limit super admin access
   - Regularly audit tenant permissions
   - Monitor cross-tenant access attempts

4. **Performance**:
   - Monitor resource usage per tenant
   - Set appropriate resource limits
   - Implement tenant-specific caching
