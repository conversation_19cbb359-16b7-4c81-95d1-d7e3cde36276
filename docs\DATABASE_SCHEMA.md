# Multi-Tenant Database Schema Documentation

## Overview
This document provides a comprehensive overview of the Bentakon Store multi-tenant database schema, including table structures, relationships, tenant isolation, and security considerations.

## Multi-Tenant Architecture
The system supports multiple tenants (storefronts) sharing the same database with complete data isolation. Each tenant represents a separate gaming store with its own products, users, orders, and configuration.

## Multi-Tenant Entity Relationship Diagram

```
┌─────────────┐
│   tenants   │ ← Central tenant management
│             │
│ id (PK)     │
│ name        │
│ slug (UK)   │
│ custom_dom  │
│ theme_conf  │
│ settings    │
│ status      │
└─────────────┘
       │
       │ (All tables below include tenant_id FK)
       │
┌─────────────┐    ┌─────────────┐    ┌─────────────┐
│user_profiles│    │  products   │    │  packages   │
│             │    │             │    │             │
│ id (PK)     │    │ id (PK)     │    │ id (PK)     │
│ tenant_id   │    │ tenant_id   │    │ tenant_id   │
│ name        │    │ slug        │    │ product_id  │
│ role        │    │ title       │    │ name        │
│ wallet_bal  │    │ description │    │ price       │
│ settings    │    │ category    │    │ ...         │
└─────────────┘    └─────────────┘    └─────────────┘
       │                   │                   │
       │                   │                   │
       │                   └───────────────────┘
       │                                       │
       │            ┌─────────────┐           │
       │            │   orders    │           │
       │            │             │           │
       │            │ id (PK)     │           │
       │            │ tenant_id   │           │
       └────────────│ user_id     │           │
                    │ product_id  │───────────┘
                    │ package_id  │───────────┘
                    │ amount      │
                    │ status      │
                    └─────────────┘
                           │
                           │
                    ┌─────────────┐
                    │digital_codes│
                    │             │
                    │ id (PK)     │
                    │ tenant_id   │
                    │ package_id  │
                    │ key_encrypt │
                    │ used        │
                    │ assigned_to │
                    └─────────────┘

Additional tenant-scoped tables:
- banner_slides (homepage banners)
- homepage_sections (product sections)
- custom_fields (product form fields)
- dropdowns (selection options)
```

## Table Definitions

### 1. tenants (Multi-Tenant Core)
**Purpose**: Central tenant management for multi-tenant architecture
**Type**: Core table for tenant isolation and configuration

```sql
CREATE TABLE tenants (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  name TEXT NOT NULL,
  slug TEXT UNIQUE NOT NULL,
  custom_domain TEXT UNIQUE,
  theme_config JSONB DEFAULT '{}',
  settings JSONB DEFAULT '{}',
  status TEXT NOT NULL DEFAULT 'active' CHECK (status IN ('active', 'inactive', 'suspended')),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

**Theme Configuration Structure**:
```json
{
  "primaryColor": "#3b82f6",
  "secondaryColor": "#1e40af",
  "accentColor": "#f59e0b",
  "backgroundColor": "#111827",
  "textColor": "#ffffff",
  "logo": "https://example.com/logo.png",
  "favicon": "https://example.com/favicon.ico",
  "customCSS": "/* Custom styles */"
}
```

**Settings Structure**:
```json
{
  "features": {
    "digitalCodes": true,
    "walletSystem": true,
    "customFields": true,
    "analytics": true
  },
  "limits": {
    "maxProducts": 1000,
    "maxUsers": 10000,
    "maxOrders": 100000
  }
}
```

### 2. user_profiles (Supabase Auth Integration)
**Purpose**: Store user profiles and authentication data with tenant association
**Type**: Extends Supabase auth.users table with multi-tenant support

```sql
CREATE TABLE user_profiles (
  id UUID REFERENCES auth.users(id) PRIMARY KEY,
  tenant_id UUID NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,
  name TEXT NOT NULL,
  role TEXT NOT NULL CHECK (role IN ('admin', 'distributor', 'user')),
  wallet_balance DECIMAL(10,2) DEFAULT 0.00,
  avatar TEXT,
  phone TEXT,
  settings JSONB DEFAULT '{}',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Indexes
CREATE INDEX idx_user_profiles_tenant_id ON user_profiles(tenant_id);
CREATE INDEX idx_user_profiles_role ON user_profiles(tenant_id, role);

-- RLS Policies (Tenant-Aware)
ALTER TABLE user_profiles ENABLE ROW LEVEL SECURITY;
CREATE POLICY "Users can view own profile" ON user_profiles FOR SELECT USING (auth.uid() = id);
CREATE POLICY "Users can update own profile" ON user_profiles FOR UPDATE USING (auth.uid() = id);
CREATE POLICY "Admins can view profiles in their tenant" ON user_profiles FOR SELECT USING (
  EXISTS (
    SELECT 1 FROM user_profiles up
    WHERE up.id = auth.uid()
    AND up.role = 'admin'
    AND (up.tenant_id = user_profiles.tenant_id OR (up.settings->>'is_super_admin')::boolean = true)
  )
);
```

### 3. products
**Purpose**: Store game and digital service information with tenant isolation
**Relationships**: Many-to-One with tenants, One-to-Many with packages, custom_fields, dropdowns

```sql
CREATE TABLE products (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  tenant_id UUID NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,
  slug TEXT NOT NULL,
  title TEXT NOT NULL,
  description TEXT NOT NULL,
  cover_image TEXT NOT NULL,
  category TEXT NOT NULL,
  tags TEXT[] DEFAULT '{}',
  rating DECIMAL(2,1) DEFAULT 0.0 CHECK (rating >= 0 AND rating <= 5),
  comment_count INTEGER DEFAULT 0,
  featured BOOLEAN DEFAULT FALSE,
  popular BOOLEAN DEFAULT FALSE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Indexes (Tenant-Aware)
CREATE UNIQUE INDEX idx_products_tenant_slug ON products(tenant_id, slug);
CREATE INDEX idx_products_tenant_id ON products(tenant_id);
CREATE INDEX idx_products_category ON products(tenant_id, category);
CREATE INDEX idx_products_featured ON products(tenant_id, featured);
CREATE INDEX idx_products_popular ON products(tenant_id, popular);
CREATE INDEX idx_products_search ON products USING gin(to_tsvector('english', title || ' ' || description));

-- RLS Policies (Tenant-Aware)
ALTER TABLE products ENABLE ROW LEVEL SECURITY;
CREATE POLICY "Products are viewable by everyone in same tenant" ON products FOR SELECT USING (
  tenant_id = (
    SELECT COALESCE(
      (SELECT tenant_id FROM user_profiles WHERE id = auth.uid()),
      (SELECT id FROM tenants WHERE slug = 'main')
    )
  )
);
CREATE POLICY "Admins can manage products in their tenant" ON products FOR ALL USING (
  EXISTS (
    SELECT 1 FROM user_profiles up
    WHERE up.id = auth.uid()
    AND up.role = 'admin'
    AND (up.tenant_id = products.tenant_id OR (up.settings->>'is_super_admin')::boolean = true)
  )
);
```

### 3. packages
**Purpose**: Store different purchase options for products
**Relationships**: Many-to-One with products, One-to-Many with digital_codes

```sql
CREATE TABLE packages (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  product_id UUID REFERENCES products(id) ON DELETE CASCADE,
  name TEXT NOT NULL,
  price DECIMAL(10,2) NOT NULL CHECK (price >= 0),
  original_price DECIMAL(10,2) CHECK (original_price >= price),
  discount INTEGER GENERATED ALWAYS AS (
    CASE 
      WHEN original_price IS NOT NULL AND original_price > 0 
      THEN ROUND(((original_price - price) / original_price * 100)::numeric, 0)::integer
      ELSE 0 
    END
  ) STORED,
  image TEXT NOT NULL,
  description TEXT,
  has_digital_codes BOOLEAN DEFAULT FALSE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Indexes
CREATE INDEX idx_packages_product_id ON packages(product_id);
CREATE INDEX idx_packages_has_digital_codes ON packages(has_digital_codes);

-- RLS Policies
ALTER TABLE packages ENABLE ROW LEVEL SECURITY;
CREATE POLICY "Packages are viewable by everyone" ON packages FOR SELECT USING (true);
CREATE POLICY "Only admins can modify packages" ON packages FOR ALL USING (
  EXISTS (SELECT 1 FROM user_profiles WHERE id = auth.uid() AND role = 'admin')
);
```

### 4. digital_codes
**Purpose**: Store redeemable codes for digital products
**Relationships**: Many-to-One with packages, One-to-One with orders
**Security**: Keys are encrypted at rest

```sql
CREATE TABLE digital_codes (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  package_id UUID REFERENCES packages(id) ON DELETE CASCADE,
  key_encrypted TEXT NOT NULL, -- Encrypted digital code
  used BOOLEAN DEFAULT FALSE,
  assigned_to_order_id UUID REFERENCES orders(id),
  assigned_at TIMESTAMP WITH TIME ZONE,
  viewed_count INTEGER DEFAULT 0,
  last_viewed_at TIMESTAMP WITH TIME ZONE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Indexes
CREATE INDEX idx_digital_codes_package_id ON digital_codes(package_id);
CREATE INDEX idx_digital_codes_used ON digital_codes(used);
CREATE INDEX idx_digital_codes_assigned_to_order_id ON digital_codes(assigned_to_order_id);

-- RLS Policies
ALTER TABLE digital_codes ENABLE ROW LEVEL SECURITY;
CREATE POLICY "Users can view codes assigned to their orders" ON digital_codes FOR SELECT USING (
  EXISTS (
    SELECT 1 FROM orders 
    WHERE orders.id = digital_codes.assigned_to_order_id 
    AND orders.user_id = auth.uid()
  )
);
CREATE POLICY "Admins can manage all digital codes" ON digital_codes FOR ALL USING (
  EXISTS (SELECT 1 FROM user_profiles WHERE id = auth.uid() AND role = 'admin')
);
```

### 5. orders
**Purpose**: Store customer purchase transactions
**Relationships**: Many-to-One with users, products, packages

```sql
CREATE TABLE orders (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES user_profiles(id) ON DELETE CASCADE,
  product_id UUID REFERENCES products(id),
  package_id UUID REFERENCES packages(id),
  amount DECIMAL(10,2) NOT NULL CHECK (amount >= 0),
  status TEXT NOT NULL CHECK (status IN ('pending', 'completed', 'failed')),
  custom_data JSONB DEFAULT '{}',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Indexes
CREATE INDEX idx_orders_user_id ON orders(user_id);
CREATE INDEX idx_orders_product_id ON orders(product_id);
CREATE INDEX idx_orders_status ON orders(status);
CREATE INDEX idx_orders_created_at ON orders(created_at);

-- RLS Policies
ALTER TABLE orders ENABLE ROW LEVEL SECURITY;
CREATE POLICY "Users can view own orders" ON orders FOR SELECT USING (user_id = auth.uid());
CREATE POLICY "Users can create own orders" ON orders FOR INSERT WITH CHECK (user_id = auth.uid());
CREATE POLICY "Admins can view all orders" ON orders FOR ALL USING (
  EXISTS (SELECT 1 FROM user_profiles WHERE id = auth.uid() AND role = 'admin')
);
```

## Security Considerations

### 1. Row Level Security (RLS)
- All tables have RLS enabled
- Users can only access their own data
- Admins have elevated permissions
- Public data (products, packages) is readable by everyone

### 2. Data Encryption
- Digital codes are encrypted using Supabase's built-in encryption
- Sensitive user data is protected
- API keys and secrets are stored securely

### 3. Audit Trail
- All tables include created_at and updated_at timestamps
- Order status changes are logged
- Digital code access is tracked

## Performance Optimizations

### 1. Indexing Strategy
- Primary keys on all tables
- Foreign key indexes for joins
- Search indexes for product discovery
- Composite indexes for common query patterns

### 2. Query Optimization
- Use of materialized views for complex aggregations
- Proper use of LIMIT and OFFSET for pagination
- Efficient joins using indexed columns

### 3. Caching Strategy
- Product catalog cached at application level
- Homepage configuration cached
- User sessions cached for performance

## Backup and Recovery

### 1. Automated Backups
- Daily automated backups via Supabase
- Point-in-time recovery available
- Cross-region backup replication

### 2. Data Retention
- Order data retained indefinitely for accounting
- User activity logs retained for 1 year
- Digital code access logs retained for 6 months
