"use client"

import { useState } from "react"
import { Minus, Plus } from "lucide-react"
import type { Package } from "../types"
import { convertAndFormatPrice } from "../utils/currency"
import { Money } from "./Money"

interface PackageSelectorProps {
  selectedPackage: Package | null
  onTopUp: (quantity: number) => void
  className?: string
}

export default function PackageSelector({ 
  selectedPackage, 
  onTopUp, 
  className = "" 
}: PackageSelectorProps) {
  const [quantity, setQuantity] = useState(1)

  if (!selectedPackage) return null

  const hasDiscount = selectedPackage.discount && selectedPackage.discount > 0
  const totalPrice = selectedPackage.price * quantity
  const originalTotalPrice = selectedPackage.originalPrice 
    ? selectedPackage.originalPrice * quantity 
    : null

  const handleQuantityChange = (newQuantity: number) => {
    if (newQuantity >= 1) {
      setQuantity(newQuantity)
    }
  }

  const handleTopUp = () => {
    onTopUp(quantity)
  }

  // Check if package supports quantity (has digital codes with stock)
  const supportsQuantity = selectedPackage.hasDigitalCodes && 
    selectedPackage.availableCodesCount && 
    selectedPackage.availableCodesCount > 1

  return (
    <div className={`fixed bottom-0 left-0 right-0 z-50 ${className}`}>
      <div className="bg-gray-900/95 backdrop-blur-md border-t border-gray-700/50 p-4">
        <div className="container mx-auto max-w-md">
          {/* Package Info */}
          <div className="mb-4">
            <h3 className="font-bold text-white mb-1">{selectedPackage.name}</h3>
            <div className="flex items-center justify-between">
              <div className="space-y-1">
                <div className="text-2xl font-bold text-purple-400">
                  <Money usdAmount={totalPrice} />
                </div>
                {hasDiscount && originalTotalPrice && (
                  <div className="flex items-center space-x-2 space-x-reverse">
                    <span className="text-sm text-gray-400 line-through">
                      <Money usdAmount={originalTotalPrice} />
                    </span>
                    <span className="text-sm text-green-400 font-medium">
                      وفر <Money usdAmount={originalTotalPrice - totalPrice} />
                    </span>
                  </div>
                )}
              </div>

              {/* Quantity Selector - Only show if package supports it */}
              {supportsQuantity && (
                <div className="flex items-center space-x-3 space-x-reverse">
                  <span className="text-sm text-gray-400">الكمية</span>
                  <div className="flex items-center space-x-2 space-x-reverse bg-gray-800 rounded-lg">
                    <button
                      onClick={() => handleQuantityChange(quantity - 1)}
                      disabled={quantity <= 1}
                      className="p-2 hover:bg-gray-700 rounded-lg transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      <Minus className="w-4 h-4" />
                    </button>
                    <span className="px-3 py-2 min-w-[3rem] text-center font-medium">
                      {quantity}
                    </span>
                    <button
                      onClick={() => handleQuantityChange(quantity + 1)}
                      disabled={selectedPackage.availableCodesCount ? quantity >= selectedPackage.availableCodesCount : false}
                      className="p-2 hover:bg-gray-700 rounded-lg transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      <Plus className="w-4 h-4" />
                    </button>
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* Top Up Button */}
          <button
            onClick={handleTopUp}
            disabled={selectedPackage.hasDigitalCodes && selectedPackage.availableCodesCount === 0}
            className="w-full bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 disabled:from-gray-600 disabled:to-gray-600 text-white font-bold py-4 px-6 rounded-xl transition-all duration-200 hover:shadow-lg hover:shadow-purple-500/25 disabled:cursor-not-allowed disabled:opacity-50"
          >
            {selectedPackage.hasDigitalCodes && selectedPackage.availableCodesCount === 0 
              ? "نفد المخزون" 
              : "شحن الآن"
            }
          </button>

          {/* Stock Warning */}
          {selectedPackage.hasDigitalCodes && selectedPackage.availableCodesCount && selectedPackage.availableCodesCount <= 5 && (
            <div className="mt-2 text-center">
              <span className="text-xs text-orange-400">
                متبقي {selectedPackage.availableCodesCount} فقط في المخزون
              </span>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}
