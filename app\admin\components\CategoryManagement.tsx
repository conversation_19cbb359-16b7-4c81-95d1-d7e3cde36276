"use client"

import { useState, useEffect } from "react"
import { Plus, Edit, Trash2, X, Save, ArrowUp, ArrowDown, Tag, Palette } from "lucide-react"
import type { Category } from "../../types"
import { useToast } from "../../components/Toast"
import { LoadingButton } from "../../components/LoadingStates"
import { useTenant } from "../../contexts/TenantContext"
import {
  getCategories,
  createCategory,
  updateCategory,
  deleteCategory,
  generateSlug,
  categoryCreateSchema,
  categoryUpdateSchema
} from "../../lib/categories"

export default function CategoryManagement() {
  const { tenant } = useTenant()
  const toast = useToast()
  
  const [categories, setCategories] = useState<Category[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [isModalOpen, setIsModalOpen] = useState(false)
  const [editingCategory, setEditingCategory] = useState<Category | null>(null)
  const [isSubmitting, setIsSubmitting] = useState(false)

  const [formData, setFormData] = useState({
    name: "",
    slug: "",
    description: "",
    image: ""
  })



  useEffect(() => {
    if (tenant) {
      loadCategories()
    }
  }, [tenant])

  const loadCategories = async () => {
    if (!tenant) return

    setIsLoading(true)
    try {
      const result = await getCategories(tenant.id)
      if (result.success && result.data) {
        setCategories(result.data)
      } else {
        toast.error('فشل في تحميل الفئات', result.error)
      }
    } catch (error) {
      toast.error('حدث خطأ في تحميل الفئات')
      console.error('Error loading categories:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const resetForm = () => {
    setFormData({
      name: "",
      slug: "",
      description: "",
      image: ""
    })
    setEditingCategory(null)
  }

  const openModal = (category?: Category) => {
    if (category) {
      setEditingCategory(category)
      setFormData({
        name: category.name || "",
        slug: category.slug,
        description: category.description || "",
        image: category.image
      })
    } else {
      resetForm()
    }
    setIsModalOpen(true)
  }

  const closeModal = () => {
    setIsModalOpen(false)
    resetForm()
  }

  const handleNameChange = (name: string) => {
    setFormData(prev => ({
      ...prev,
      name,
      slug: prev.slug || generateSlug(name) // Auto-generate slug if empty
    }))
  }

  const handleSave = async () => {
    if (!tenant) return

    setIsSubmitting(true)
    try {
      let result

      if (editingCategory) {
        // Update existing category
        result = await updateCategory(editingCategory.id, formData, tenant.id)
        if (result.success) {
          toast.success('تم تحديث الفئة بنجاح')
          setCategories(prev => 
            prev.map(cat => cat.id === editingCategory.id ? { ...cat, ...result.data } : cat)
          )
        } else {
          toast.error('فشل في تحديث الفئة', result.error)
        }
      } else {
        // Create new category
        result = await createCategory(formData, tenant.id)
        if (result.success && result.data) {
          toast.success('تم إضافة الفئة بنجاح')
          setCategories(prev => [...prev, result.data!])
        } else {
          toast.error('فشل في إضافة الفئة', result.error)
        }
      }

      if (result.success) {
        closeModal()
      }
    } catch (error) {
      toast.error('حدث خطأ غير متوقع')
      console.error('Category save error:', error)
    } finally {
      setIsSubmitting(false)
    }
  }

  const handleDelete = async (categoryId: string) => {
    if (!tenant) return
    if (!confirm("هل أنت متأكد من حذف هذه الفئة؟")) return

    try {
      const result = await deleteCategory(categoryId, tenant.id)
      if (result.success) {
        toast.success('تم حذف الفئة بنجاح')
        setCategories(prev => prev.filter(cat => cat.id !== categoryId))
      } else {
        toast.error('فشل في حذف الفئة', result.error)
      }
    } catch (error) {
      toast.error('حدث خطأ غير متوقع')
      console.error('Category delete error:', error)
    }
  }



  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <h2 className="text-xl md:text-2xl font-bold">إدارة الفئات</h2>
        </div>
        <div className="bg-gray-800/50 backdrop-blur-md rounded-xl border border-gray-700/50 shadow-xl p-6">
          <div className="animate-pulse space-y-4">
            {[1, 2, 3].map(i => (
              <div key={i} className="h-16 bg-gray-700/50 rounded-lg"></div>
            ))}
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
        <h2 className="text-xl md:text-2xl font-bold">إدارة الفئات</h2>
        <button 
          onClick={() => openModal()} 
          className="btn-primary flex items-center justify-center space-x-2 space-x-reverse w-full sm:w-auto"
        >
          <Plus className="w-5 h-5" />
          <span>إضافة فئة</span>
        </button>
      </div>

      {/* Categories List */}
      <div className="bg-gray-800/50 backdrop-blur-md rounded-xl border border-gray-700/50 shadow-xl">
        <div className="p-4 md:p-6">
          {categories.length === 0 ? (
            <div className="text-center py-12">
              <Tag className="w-16 h-16 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-semibold text-gray-300 mb-2">لا توجد فئات</h3>
              <p className="text-gray-400 mb-6">ابدأ بإضافة فئة جديدة لتنظيم منتجاتك</p>
              <button 
                onClick={() => openModal()} 
                className="btn-primary"
              >
                إضافة فئة جديدة
              </button>
            </div>
          ) : (
            <div className="space-y-3">
              {categories.map((category, index) => (
                <div 
                  key={category.id} 
                  className="bg-gray-700/30 backdrop-blur-sm rounded-xl p-4 border border-gray-600/50 hover:border-gray-500/50 transition-all duration-300"
                >
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-4 space-x-reverse flex-1">
                      {/* Category Image */}
                      <div className="w-16 h-16 rounded-xl overflow-hidden bg-gray-600/50">
                        <img
                          src={category.image}
                          alt={category.name || category.slug}
                          className="w-full h-full object-cover"
                          onError={(e) => {
                            const target = e.target as HTMLImageElement
                            target.src = "/logo.jpg"
                          }}
                        />
                      </div>

                      {/* Category Info */}
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center space-x-2 space-x-reverse mb-1">
                          <h3 className="font-semibold text-lg truncate">
                            {category.name || category.slug}
                          </h3>
                        </div>
                        <p className="text-sm text-gray-400 truncate">
                          {category.description || 'لا يوجد وصف'}
                        </p>
                        <div className="flex items-center space-x-4 space-x-reverse mt-2 text-xs text-gray-500">
                          <span>الرابط: {category.slug}</span>
                          <span>المنتجات: {category.product_count || 0}</span>
                        </div>
                      </div>
                    </div>

                    {/* Actions */}
                    <div className="flex items-center space-x-2 space-x-reverse">
                      {/* Edit button */}
                      <button
                        onClick={() => openModal(category)}
                        className="p-2 text-blue-400 hover:text-blue-300 hover:bg-blue-400/10 rounded-lg transition-all duration-300"
                      >
                        <Edit className="w-4 h-4" />
                      </button>

                      {/* Delete button */}
                      <button
                        onClick={() => handleDelete(category.id)}
                        className="p-2 text-red-400 hover:text-red-300 hover:bg-red-400/10 rounded-lg transition-all duration-300"
                        disabled={(category.product_count || 0) > 0}
                        title={(category.product_count || 0) > 0 ? "لا يمكن حذف فئة تحتوي على منتجات" : "حذف الفئة"}
                      >
                        <Trash2 className="w-4 h-4" />
                      </button>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      </div>

      {/* Category Modal */}
      {isModalOpen && (
        <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center p-4 z-50">
          <div className="bg-gray-800/90 backdrop-blur-md rounded-2xl border border-gray-700/50 shadow-2xl w-full max-w-2xl max-h-[90vh] overflow-y-auto">
            <div className="p-4 md:p-6 border-b border-gray-700/50">
              <div className="flex items-center justify-between">
                <h3 className="text-xl font-bold">
                  {editingCategory ? 'تعديل الفئة' : 'إضافة فئة جديدة'}
                </h3>
                <button
                  onClick={closeModal}
                  className="p-2 text-gray-400 hover:text-white hover:bg-gray-700/50 rounded-lg transition-all duration-300"
                >
                  <X className="w-5 h-5" />
                </button>
              </div>
            </div>

            <div className="p-4 md:p-6 space-y-6">
              {/* Basic Info */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium mb-2">اسم الفئة</label>
                  <input
                    type="text"
                    value={formData.name}
                    onChange={(e) => handleNameChange(e.target.value)}
                    className="w-full bg-gray-700/50 backdrop-blur-sm border border-gray-600/50 rounded-lg px-3 py-2 focus:outline-none focus:border-purple-500 focus:ring-2 focus:ring-purple-500/20 transition-all duration-300"
                    placeholder="أدخل اسم الفئة (اختياري)"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium mb-2">الرابط المختصر *</label>
                  <input
                    type="text"
                    value={formData.slug}
                    onChange={(e) => setFormData(prev => ({ ...prev, slug: e.target.value }))}
                    className="w-full bg-gray-700/50 backdrop-blur-sm border border-gray-600/50 rounded-lg px-3 py-2 focus:outline-none focus:border-purple-500 focus:ring-2 focus:ring-purple-500/20 transition-all duration-300"
                    placeholder="category-name-here"
                    required
                  />
                  <p className="text-xs text-gray-400 mt-1">
                    يجب أن يكون بصيغة: name-name-name (أحرف صغيرة وشرطات فقط)
                  </p>
                </div>
              </div>

              {/* Image */}
              <div>
                <label className="block text-sm font-medium mb-2">صورة الفئة *</label>
                <input
                  type="url"
                  value={formData.image}
                  onChange={(e) => setFormData(prev => ({ ...prev, image: e.target.value }))}
                  className="w-full bg-gray-700/50 backdrop-blur-sm border border-gray-600/50 rounded-lg px-3 py-2 focus:outline-none focus:border-purple-500 focus:ring-2 focus:ring-purple-500/20 transition-all duration-300"
                  placeholder="https://example.com/image.jpg"
                  required
                />
              </div>

              {/* Description */}
              <div>
                <label className="block text-sm font-medium mb-2">الوصف</label>
                <textarea
                  value={formData.description}
                  onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                  rows={3}
                  className="w-full bg-gray-700/50 backdrop-blur-sm border border-gray-600/50 rounded-lg px-3 py-2 focus:outline-none focus:border-purple-500 focus:ring-2 focus:ring-purple-500/20 transition-all duration-300"
                  placeholder="وصف اختياري للفئة"
                />
              </div>

              {/* Preview */}
              {(formData.image || formData.name || formData.slug) && (
                <div>
                  <label className="block text-sm font-medium mb-2">معاينة</label>
                  <div className="bg-gray-700/30 backdrop-blur-sm rounded-xl p-4 border border-gray-600/50">
                    <div className="flex items-center space-x-3 space-x-reverse">
                      <div className="w-16 h-16 rounded-xl overflow-hidden bg-gray-600/50">
                        <img
                          src={formData.image || "/logo.jpg"}
                          alt="Category preview"
                          className="w-full h-full object-cover"
                          onError={(e) => {
                            const target = e.target as HTMLImageElement
                            target.src = "/logo.jpg"
                          }}
                        />
                      </div>
                      <div>
                        <h4 className="font-semibold">
                          {formData.name || formData.slug || 'اسم الفئة'}
                        </h4>
                        <p className="text-sm text-gray-400">
                          {formData.description || 'وصف الفئة'}
                        </p>
                        <p className="text-xs text-gray-500">
                          الرابط: {formData.slug || 'category-slug'}
                        </p>
                      </div>
                    </div>
                  </div>
                </div>
              )}
            </div>

            {/* Modal Footer */}
            <div className="p-4 md:p-6 border-t border-gray-700/50 flex items-center justify-end space-x-3 space-x-reverse">
              <button
                onClick={closeModal}
                className="btn-secondary"
                disabled={isSubmitting}
              >
                إلغاء
              </button>
              <LoadingButton
                onClick={handleSave}
                loading={isSubmitting}
                className="btn-primary"
                disabled={!formData.slug.trim() || !formData.image.trim()}
              >
                <Save className="w-4 h-4" />
                <span>{editingCategory ? 'تحديث' : 'إضافة'}</span>
              </LoadingButton>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}